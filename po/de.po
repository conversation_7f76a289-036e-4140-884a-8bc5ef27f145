# German translations for dnsmasq package.
#
# This revised version is (C) Copyright by
# <PERSON> <<EMAIL>>, 2010 - 2021.
# <PERSON> <<EMAIL>>, 2014 - 2024.
# It is subject to the GNU General Public License v2,
# or at your option, any later version.
#
# An older version of this file was originally put in the public domain by
# <PERSON> <<EMAIL>>, 2005.
msgid ""
msgstr ""
"Project-Id-Version: dnsmasq 2.91\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-03-20 00:00+0000\n"
"PO-Revision-Date: 2024-12-23 22:36+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: German <<EMAIL>>\n"
"Language: de\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.5\n"

#: cache.c:652
msgid "Internal error in cache."
msgstr "Interner Fehler im Cache."

#: cache.c:1179
#, c-format
msgid "failed to load names from %s: %s"
msgstr "Fehler beim Laden der Namen von %s: %s"

#: cache.c:1201 dhcp.c:943
#, c-format
msgid "bad address at %s line %d"
msgstr "Fehlerhafte Adresse in %s Zeile %d"

#: cache.c:1254 dhcp.c:959
#, c-format
msgid "bad name at %s line %d"
msgstr "Fehlerhafter Name in %s Zeile %d"

#: cache.c:1265
#, c-format
msgid "read %s - %d names"
msgstr "%s gelesen - %d Namen"

#: cache.c:1381
msgid "cleared cache"
msgstr "Cache geleert"

#: cache.c:1445
#, c-format
msgid "No IPv4 address found for %s"
msgstr "Keine IPv4-Adresse für %s gefunden"

#: cache.c:1491
#, c-format
msgid "%s is a CNAME, not giving it to the DHCP lease of %s"
msgstr "%s ist ein CNAME, weise es der DHCP-Lease von %s nicht zu"

#: cache.c:1515
#, c-format
msgid "not giving name %s to the DHCP lease of %s because the name exists in %s with address %s"
msgstr "Name %s wurde dem DHCP-Lease von %s nicht zugewiesen, da der Name in %s bereits mit Adresse %s existiert"

#: cache.c:1760
#, c-format
msgid "time %lu"
msgstr "Zeit %lu"

#: cache.c:1761
#, c-format
msgid "cache size %d, %d/%d cache insertions re-used unexpired cache entries."
msgstr "Cache Größe %d, %d/%d Cache-Einfügungen verwendeten nicht abgelaufene Cache-Einträge wieder."

#: cache.c:1763
#, c-format
msgid "queries forwarded %u, queries answered locally %u"
msgstr "weitergeleitete Anfragen %u, lokal beantwortete Anfragen %u"

#: cache.c:1766
#, c-format
msgid "queries answered from stale cache %u"
msgstr "Anfragen beantwortet vom veralteten Cache %u"

#: cache.c:1768
#, c-format
msgid "queries for authoritative zones %u"
msgstr "Anfragen nach autoritativen Zonen %u"

#: cache.c:1796
#, c-format
msgid "server %s#%d: queries sent %u, retried %u, failed %u, nxdomain replies %u, avg. latency %ums"
msgstr "Server %s#%d: Anfragen gesendet %u, erneut versucht %u, fehlgeschlagen %u, nxdomain-Antworten %u, durchschnittliche Latenz %ums"

#: util.c:51
#, c-format
msgid "failed to seed the random number generator: %s"
msgstr "Konnte den Zufallszahlengenerator nicht initialisieren: %s"

#: util.c:246
msgid "failed to allocate memory"
msgstr "Konnte Speicher nicht belegen"

#: util.c:305 option.c:696
msgid "could not get memory"
msgstr "Speicher nicht verfügbar"

#: util.c:326
#, c-format
msgid "cannot create pipe: %s"
msgstr "Konnte Pipe nicht erzeugen: %s"

#: util.c:334
#, c-format
msgid "failed to allocate %d bytes"
msgstr "Konnte %d Bytes nicht belegen"

#: util.c:344
#, c-format
msgid "failed to reallocate %d bytes"
msgstr "Konnte %d Bytes nicht allozieren"

#: util.c:465
#, c-format
msgid "cannot read monotonic clock: %s"
msgstr "monotone Uhr kann nicht gelesen werden: %s"

# @Simon: not perfect but I cannot get nearer right now.
#: util.c:579
#, c-format
msgid "infinite"
msgstr "unendlich"

#: util.c:867
#, c-format
msgid "failed to find kernel version: %s"
msgstr "konnte Kernelversion nicht finden: %s"

#: option.c:393
msgid "Specify local address(es) to listen on."
msgstr "Lokale abzuhörende Adresse(n) angeben."

#: option.c:394
msgid "Return ipaddr for all hosts in specified domains."
msgstr "IP-Adresse für alle Hosts in angegebenen Domänen festlegen."

#: option.c:395
msgid "Fake reverse lookups for RFC1918 private address ranges."
msgstr "Rückwärtsauflösungen für private Adressbereiche nach RFC1918 falsch erfinden."

#: option.c:396
msgid "Treat ipaddr as NXDOMAIN (defeats Verisign wildcard)."
msgstr "Diese IP-Adresse als NXDOMAIN interpretieren (wehrt \"Suchhilfen\" ab)."

#: option.c:397
#, c-format
msgid "Specify the size of the cache in entries (defaults to %s)."
msgstr "Größe des Caches (Zahl der Einträge) festlegen (Voreinstellung: %s)."

#: option.c:398
#, c-format
msgid "Specify configuration file (defaults to %s)."
msgstr "Konfigurationsdatei festlegen (Voreinstellung: %s)."

#: option.c:399
msgid "Do NOT fork into the background: run in debug mode."
msgstr "NICHT in den Hintergrund gehen: Betrieb im Debug-Modus."

#: option.c:400
msgid "Do NOT forward queries with no domain part."
msgstr "Anfragen ohne Domänen-Teil NICHT weiterschicken."

#: option.c:401
msgid "Return self-pointing MX records for local hosts."
msgstr "Für lokale Einträge MX-Einträge liefern, die auf sich selbst zeigen."

#: option.c:402
msgid "Expand simple names in /etc/hosts with domain-suffix."
msgstr "Einfache Namen in /etc/hosts um Domänen-Endung erweitern."

#: option.c:403
msgid "Don't forward spurious DNS requests from Windows hosts."
msgstr "Unberechtigte DNS-Anfragen von Windows-Rechnern nicht weiterleiten."

#: option.c:404
msgid "Don't include IPv4 addresses in DNS answers."
msgstr "Keine IPv4-Adressen in DNS-Antworten inkludieren."

#: option.c:405
msgid "Don't include IPv6 addresses in DNS answers."
msgstr "Keine IPv6-Adressen in DNS-Antworten inkludieren."

#: option.c:406
msgid "Enable DHCP in the range given with lease duration."
msgstr "DHCP für angegebenen Bereich und Lease-Dauer einschalten."

#: option.c:407
#, c-format
msgid "Change to this group after startup (defaults to %s)."
msgstr "Nach dem Start in diese Benutzergruppe wechseln (Voreinstellung %s)."

#: option.c:408
msgid "Set address or hostname for a specified machine."
msgstr "Adresse oder Hostnamen für einen angegebenen Computer setzen."

#: option.c:409
msgid "Read DHCP host specs from file."
msgstr "DHCP-Host-Angaben aus Datei lesen."

#: option.c:410
msgid "Read DHCP option specs from file."
msgstr "DHCP-Optionen aus Datei lesen."

#: option.c:411
msgid "Read DHCP host specs from a directory."
msgstr "DHCP-Host-Angaben aus einem Verzeichnis lesen."

#: option.c:412
msgid "Read DHCP options from a directory."
msgstr "DHCP-Optionen aus einem Verzeichnis lesen."

#: option.c:413
msgid "Evaluate conditional tag expression."
msgstr "Auswertung eines Ausdrucks bedingter Marken."

#: option.c:414
#, c-format
msgid "Do NOT load %s file."
msgstr "%s-Datei NICHT laden."

#: option.c:415
#, c-format
msgid "Specify a hosts file to be read in addition to %s."
msgstr "Hosts-Datei festlegen, die zusätzlich zu %s gelesen wird."

#: option.c:416
msgid "Read hosts files from a directory."
msgstr "DHCP-Host-Dateien aus einem Verzeichnis lesen."

#: option.c:417
msgid "Specify interface(s) to listen on."
msgstr "Schnittstelle(n) zum Empfang festlegen."

#: option.c:418
msgid "Specify interface(s) NOT to listen on."
msgstr "Schnittstelle(n) festlegen, die NICHT empfangen sollen."

#: option.c:419
msgid "Map DHCP user class to tag."
msgstr "DHCP-Benutzerklasse auf Marke abbilden."

#: option.c:420
msgid "Map RFC3046 circuit-id to tag."
msgstr "RFC3046 \"circuit-id\" auf Marke abbilden."

#: option.c:421
msgid "Map RFC3046 remote-id to tag."
msgstr "RFC3046 \"remote-id\" auf Marke abbilden."

#: option.c:422
msgid "Map RFC3993 subscriber-id to tag."
msgstr "RFC3993 \"subscriber-id\" auf Marke abbilden."

#: option.c:423
msgid "Specify vendor class to match for PXE requests."
msgstr "Herstellerklasse für Vergleich von PXE-Anforderungen angeben."

#: option.c:424
msgid "Don't do DHCP for hosts with tag set."
msgstr "Kein DHCP für Hosts mit gesetzter Marke verwenden."

#: option.c:425
msgid "Force broadcast replies for hosts with tag set."
msgstr "Antwort per Broadcast für Hosts mit gesetzter Marke erzwingen."

#: option.c:426
msgid "Do NOT fork into the background, do NOT run in debug mode."
msgstr "NICHT in den Hintergrund wechseln, NICHT im Debug-Modus laufen."

#: option.c:427
msgid "Assume we are the only DHCP server on the local network."
msgstr "Unterstellen, dass wir der einzige DHCP-Server im lokalen Netz sind."

#: option.c:428
#, c-format
msgid "Specify where to store DHCP leases (defaults to %s)."
msgstr "Festlegen, wo DHCP-Leases gespeichert werden (Voreinstellung %s)."

#: option.c:429
msgid "Return MX records for local hosts."
msgstr "MX-Einträge für lokale Hosts liefern."

#: option.c:430
msgid "Specify an MX record."
msgstr "Einen MX-Eintrag festlegen."

#: option.c:431
msgid "Specify BOOTP options to DHCP server."
msgstr "BOOTP-Optionen für DHCP-Server festlegen."

#: option.c:432
#, c-format
msgid "Do NOT poll %s file, reload only on SIGHUP."
msgstr "%s-Datei NICHT abfragen, nur bei SIGHUP neu laden."

#: option.c:433
msgid "Do NOT cache failed search results."
msgstr "Fehlerhafte Suchergebnisse NICHT zwischenspeichern."

#: option.c:434
msgid "Use expired cache data for faster reply."
msgstr "Verwende abgelaufene Cachedaten für schnellere Antwort."

#: option.c:435
#, c-format
msgid "Use nameservers strictly in the order given in %s."
msgstr "Namensserver streng in der in %s angegebenen Reihenfolge verwenden."

#: option.c:436
msgid "Specify options to be sent to DHCP clients."
msgstr "Optionen festlegen, die an DHCP-Klienten gesendet werden."

#: option.c:437
msgid "DHCP option sent even if the client does not request it."
msgstr "DHCP-Option, die selbst ohne Klientenanfrage gesendet wird."

#: option.c:438
msgid "Specify port to listen for DNS requests on (defaults to 53)."
msgstr "Port zum Empfangen der DNS-Anfragen festlegen (53 voreingestellt)."

#: option.c:439
#, c-format
msgid "Maximum supported UDP packet size for EDNS.0 (defaults to %s)."
msgstr "Maximale unterstützte UDP-Paketgröße für EDNS.0 (Voreinstellung %s)."

#: option.c:440
msgid "Log DNS queries."
msgstr "DNS-Anfragen protokollieren."

#: option.c:441
msgid "Force the originating port for upstream DNS queries."
msgstr "Ausgehenden Port für DNS-Anfragen an vorgelagerte Server erzwingen."

#: option.c:442
msgid "Set maximum number of random originating ports for a query."
msgstr "Setze die maximale Anzahl zufälliger Ursprungsports für eine Abfrage."

#: option.c:443
msgid "Do NOT read resolv.conf."
msgstr "Die resolv.conf NICHT lesen."

#: option.c:444
#, c-format
msgid "Specify path to resolv.conf (defaults to %s)."
msgstr "Pfad zu resolv.conf festlegen (%s voreingestellt)."

#: option.c:445
msgid "Specify path to file with server= options"
msgstr "Pfad für Datei mit server=-Optionen angeben"

#: option.c:446
msgid "Specify address(es) of upstream servers with optional domains."
msgstr "Adresse(n) vorgelagerter Server festlegen, optional mit Domänen."

#: option.c:447
msgid "Specify address of upstream servers for reverse address queries"
msgstr "Adresse(n) vorgelagerter Server festlegen, für Rückwärtsauflösung"

#: option.c:448
msgid "Never forward queries to specified domains."
msgstr "Anfragen für angegebene Domänen niemals weiterleiten."

#: option.c:449
msgid "Specify the domain to be assigned in DHCP leases."
msgstr "Domäne festlegen, die für DHCP-Leases zugewiesen wird."

#: option.c:450
msgid "Specify default target in an MX record."
msgstr "Voreingestelltes Ziel für MX-Einträge festlegen."

#: option.c:451
msgid "Specify time-to-live in seconds for replies from /etc/hosts."
msgstr "Gültigkeitsdauer für Antworten aus /etc/hosts festlegen."

#: option.c:452
msgid "Specify time-to-live in seconds for negative caching."
msgstr "Gültigkeitsdauer in Sekunden für Zwischenspeicher negativer Ergebnisse festlegen."

#: option.c:453
msgid "Specify time-to-live in seconds for maximum TTL to send to clients."
msgstr "Gültigkeitsdauer in Sekunden für Caching negativer Ergebnisse festlegen."

#: option.c:454
msgid "Specify time-to-live ceiling for cache."
msgstr "Spezifiziere obere Gültigkeitsdauergrenze für Zwischenspeicher."

#: option.c:455
msgid "Specify time-to-live floor for cache."
msgstr "Spezifiziere untere Gültigkeitsdauergrenze für Zwischenspeicher."

#: option.c:456
msgid "Retry DNS queries after this many milliseconds."
msgstr "DNS-Abfragen nach so vielen Millisekunden wiederholen."

#: option.c:457
#, c-format
msgid "Change to this user after startup. (defaults to %s)."
msgstr "Nach dem Start diese Benutzerrechte annehmen (%s voreingestellt)."

#: option.c:458
msgid "Map DHCP vendor class to tag."
msgstr "DHCP-\"vendor class\" auf Marke abbilden."

#: option.c:459
msgid "Display dnsmasq version and copyright information."
msgstr "DNSMasq-Version und Urheberrecht anzeigen."

#: option.c:460
msgid "Translate IPv4 addresses from upstream servers."
msgstr "IPv4-Adressen von vorgelagerten Servern umsetzen."

#: option.c:461
msgid "Specify a SRV record."
msgstr "SRV-Eintrag festlegen."

#: option.c:462
msgid "Display this message. Use --help dhcp or --help dhcp6 for known DHCP options."
msgstr "Diese Hilfe anzeigen. Benutzen Sie --help dhcp oder --help dhcp6 für bekannte DHCP-Optionen."

#: option.c:463
#, c-format
msgid "Specify path of PID file (defaults to %s)."
msgstr "Pfad für Prozesskennungsdatei (PID) festlegen (Voreinstellung: %s)."

#: option.c:464
#, c-format
msgid "Specify maximum number of DHCP leases (defaults to %s)."
msgstr "Höchstzahl der DHCP-Leases festlegen (%s voreingestellt)."

#: option.c:465
msgid "Answer DNS queries based on the interface a query was sent to."
msgstr "DNS-Anfragen abhängig der Empfangsschnittstelle beantworten."

#: option.c:466
msgid "Specify TXT DNS record."
msgstr "DNS-TXT-Eintrag festlegen."

#: option.c:467
msgid "Specify PTR DNS record."
msgstr "DNS-PTR-Eintrag festlegen."

#: option.c:468
msgid "Give DNS name to IPv4 address of interface."
msgstr "Schnittstellennamen zur IPv4-Adresse der Schnittstelle auflösen."

#: option.c:469
msgid "Bind only to interfaces in use."
msgstr "Nur an verwendete Schnittstellen binden."

#: option.c:470
#, c-format
msgid "Read DHCP static host information from %s."
msgstr "Statische DHCP-Host-Information aus %s lesen."

#: option.c:471
msgid "Enable the DBus interface for setting upstream servers, etc."
msgstr "DBus-Schnittstelle zum Festlegen vorgelagerter Server usw. festlegen."

#: option.c:472
msgid "Enable the UBus interface."
msgstr "UBus-Schnittstelle aktivieren."

#: option.c:473
msgid "Do not provide DHCP on this interface, only provide DNS."
msgstr "Auf dieser Schnittstelle kein DHCP anbieten, sondern nur DNS."

#: option.c:474
msgid "Enable dynamic address allocation for bootp."
msgstr "Dynamische Adressbelegung für bootp einschalten."

#: option.c:475
msgid "Map MAC address (with wildcards) to option set."
msgstr "MAC-Adresse (mit Jokerzeichen) auf Optionenmenge abbilden."

#: option.c:476
msgid "Treat DHCP requests on aliases as arriving from interface."
msgstr "DHCP-Anfragen von Alias-Schnittstellen für die Hauptschnittstelle beantworten."

#: option.c:477
msgid "Specify extra networks sharing a broadcast domain for DHCP"
msgstr "Geben Sie zusätzliche Netzwerke an, die eine Broadcast-Domäne für DHCP gemeinsam nutzen"

#: option.c:478
msgid "Disable ICMP echo address checking in the DHCP server."
msgstr "ICMP-Echo-Adressprüfung im DHCP-Server abschalten."

#: option.c:479
msgid "Shell script to run on DHCP lease creation and destruction."
msgstr "Skript, das bei Erzeugung/Löschung einer DHCP-Lease laufen soll."

#: option.c:480
msgid "Lua script to run on DHCP lease creation and destruction."
msgstr "Lua-Skript, welches bei Erzeugung/Löschung eines DHCP-Leases laufen soll."

#: option.c:481
msgid "Run lease-change scripts as this user."
msgstr "Lease-Änderungs-Skript mit den Rechten dieses Nutzers ausführen."

#: option.c:482
msgid "Call dhcp-script with changes to local ARP table."
msgstr "Rufe dhcp-script mit Änderungen an der lokalen ARP-Tabelle auf."

#: option.c:483
msgid "Read configuration from all the files in this directory."
msgstr "Konfiguration aus allen Dateien in diesem Verzeichnis lesen."

#: option.c:484
msgid "Execute file and read configuration from stdin."
msgstr "Führe Datei aus und lese die Konfiguration aus stdin."

#: option.c:485
msgid "Log to this syslog facility or file. (defaults to DAEMON)"
msgstr "Für diese Syslog-Anlage oder in Datei loggen (Voreinstellung DAEMON)."

#: option.c:486
msgid "Do not use leasefile."
msgstr "Keine Lease-Datei benützen."

#: option.c:487
#, c-format
msgid "Maximum number of concurrent DNS queries. (defaults to %s)"
msgstr "Höchstzahl nebenläufiger DNS-Anfragen (%s voreingestellt)."

#: option.c:488
#, c-format
msgid "Clear DNS cache when reloading %s."
msgstr "DNS-Zwischenspeicher beim Neuladen von %s löschen."

#: option.c:489
msgid "Ignore hostnames provided by DHCP clients."
msgstr "Von DHCP-Klienten gelieferte Hostnamen ignorieren."

#: option.c:490
msgid "Do NOT reuse filename and server fields for extra DHCP options."
msgstr "Dateinamen und Server-Datenfelder NICHT für zusätzliche DHCP-Optionen wiederverwenden."

#: option.c:491
msgid "Enable integrated read-only TFTP server."
msgstr "Eingebauten schreibgeschützten TFTP-Server einschalten."

#: option.c:492
msgid "Export files by TFTP only from the specified subtree."
msgstr "Nur vom festgelegten Unterbaum Dateien per TFTP exportieren."

#: option.c:493
msgid "Add client IP or hardware address to tftp-root."
msgstr "IP-Adresse oder Hardware-Adresse des Klienten an tftp-root anhängen."

#: option.c:494
msgid "Allow access only to files owned by the user running dnsmasq."
msgstr "Zugriff nur auf Dateien gestatten, die dem dnsmasq betreibenden Benutzer gehören."

#: option.c:495
msgid "Do not terminate the service if TFTP directories are inaccessible."
msgstr "Den Dienst nicht beenden, wenn die TFTP-Verzeichnisse unerreichbar sind."

#: option.c:496
#, c-format
msgid "Maximum number of concurrent TFTP transfers (defaults to %s)."
msgstr "Maximale Anzahl gleichzeitiger TFTP-Übertragungen (%s voreingestellt)."

#: option.c:497
msgid "Maximum MTU to use for TFTP transfers."
msgstr "Maximale MTU für TFTP-Übertragungen erreicht."

#: option.c:498
msgid "Disable the TFTP blocksize extension."
msgstr "TFTP-Blockgrößen-Erweiterung abschalten."

#: option.c:499
msgid "Convert TFTP filenames to lowercase"
msgstr "TFTP-Dateinamen in Kleinschreibung umsetzen"

#: option.c:500
msgid "Ephemeral port range for use by TFTP transfers."
msgstr "Bereich für vorübergehende Ports für TFTP-Übertragungen."

#: option.c:501
msgid "Use only one port for TFTP server."
msgstr "Bitte nur einen Port für den TFTP-Server nutzen."

#: option.c:502
msgid "Extra logging for DHCP."
msgstr "Erweiterte DHCP-Protokollierung."

#: option.c:503
msgid "Enable async. logging; optionally set queue length."
msgstr "Asynchrone Protokollierung einschalten, opt. Warteschlangenlänge festlegen."

#: option.c:504
msgid "Stop DNS rebinding. Filter private IP ranges when resolving."
msgstr "DNS-Rebinding unterbinden, private IP-Bereiche bei der Auflösung ausfiltern."

#: option.c:505
msgid "Allow rebinding of *********/8, for RBL servers."
msgstr "Auflösung zu *********/8 erlauben, für RBL-Server."

#: option.c:506
msgid "Inhibit DNS-rebind protection on this domain."
msgstr "DNS-Rebind-Schutz für diese Domäne aufheben."

#: option.c:507
msgid "Always perform DNS queries to all servers."
msgstr "DNS-Anfragen immer an alle Server weiterleiten."

#: option.c:508
msgid "Set tag if client includes matching option in request."
msgstr "Marke setzen, wenn Klient eine entsprechende Option anfragt."

#: option.c:509
msgid "Set tag if client provides given name."
msgstr "Setzt das Tag, wenn der Client diesen Namen anbietet."

#: option.c:510
msgid "Use alternative ports for DHCP."
msgstr "Alternative Ports für DHCP verwenden."

#: option.c:511
msgid "Specify NAPTR DNS record."
msgstr "DNS-NAPTR-Eintrag festlegen."

#: option.c:512
msgid "Specify lowest port available for DNS query transmission."
msgstr "Niedrigsten verfügbaren Port für Übertragung von DNS-Anfragen festlegen."

#: option.c:513
msgid "Specify highest port available for DNS query transmission."
msgstr "Höchsten verfügbaren Port für Übertragung von DNS-Anfragen festlegen."

#: option.c:514
msgid "Use only fully qualified domain names for DHCP clients."
msgstr "Für DHCP-Klienten nur vollständig bestimmte Domänennamen benutzen."

# FIXME: probably typo in original message. -- MA
#: option.c:515
msgid "Generate hostnames based on MAC address for nameless clients."
msgstr "Generiere Hostnamen auf Basis der MAC-Adresse für namenlose Klienten."

#: option.c:516
msgid "Use these DHCP relays as full proxies."
msgstr "Diese DHCP-Relais als vollwertige Proxies verwenden."

#: option.c:517
msgid "Relay DHCP requests to a remote server"
msgstr "Leite DHCP-Anfragen an entfernten Server weiter"

#: option.c:518
msgid "Specify alias name for LOCAL DNS name."
msgstr "Alias für LOKALEN DNS-Namen festlegen."

#: option.c:519
msgid "Prompt to send to PXE clients."
msgstr "Aufforderung, die an PXE-Klienten geschickt wird."

#: option.c:520
msgid "Boot service for PXE menu."
msgstr "Boot-Dienst für PXE-Menü."

#: option.c:521
msgid "Check configuration syntax."
msgstr "Konfigurationssyntax prüfen."

#: option.c:522
msgid "Add requestor's MAC address to forwarded DNS queries."
msgstr "Anfragende MAC-Adresse in die weiterleitende DNS-Anfrage einfügen."

#: option.c:523
msgid "Strip MAC information from queries."
msgstr "Entferne die MAC-Information von Abfragen."

#: option.c:524
msgid "Add specified IP subnet to forwarded DNS queries."
msgstr "Füge spezifiziertes IP-Subnetz an weitergeleiteten DNS-Anfragen hinzu."

#: option.c:525
msgid "Strip ECS information from queries."
msgstr "Entferne die ECS-Information von Abfragen."

#: option.c:526
msgid "Add client identification to forwarded DNS queries."
msgstr "Füge Klient Identifikationan weitergeleiteten DNS-Anfragen hinzu."

# This is a rather liberal translation to convey the purpose.
# something along "authorize upstream nameservers to validate DNSSEC [for us]"
#: option.c:527
msgid "Proxy DNSSEC validation results from upstream nameservers."
msgstr "Vorgelagerte Namensserver für DNSSEC-Validierung ermächtigen."

#: option.c:528
msgid "Attempt to allocate sequential IP addresses to DHCP clients."
msgstr "Versuche, sequenzielle IP-Adressen an DHCP-Klienten zu vergeben."

#: option.c:529
msgid "Ignore client identifier option sent by DHCP clients."
msgstr "Ignorieren Sie die von DHCP-Clients gesendete Client-ID-Option."

#: option.c:530
msgid "Copy connection-track mark from queries to upstream connections."
msgstr "Kopiere \"connection-track mark\" von Anfragen nach Upstream-Verbindungen."

#: option.c:531
msgid "Allow DHCP clients to do their own DDNS updates."
msgstr "Erlaube DHCP-Klienten, ihre eigenen DDNS-Updates durchzuführen."

#: option.c:532
msgid "Send router-advertisements for interfaces doing DHCPv6"
msgstr "Sende \"Router-Advertisments\" für Netzwerkschnittstellen, welche DHCPv6 nutzen"

#: option.c:533
msgid "Specify DUID_EN-type DHCPv6 server DUID"
msgstr "Spezifiziere DUID_EN-type DHCPv6 Server DUID"

#: option.c:534
msgid "Specify host (A/AAAA and PTR) records"
msgstr "Spezifiziere Host (A/AAAA und PTR) Einträge"

#: option.c:535
msgid "Specify host record in interface subnet"
msgstr "Host-Eintrag für das Unternetzwerk der Schnittstelle angeben"

#: option.c:536
msgid "Specify certification authority authorization record"
msgstr "Autorisierungsdatensatz der Zertifizierungsstelle angeben"

#: option.c:537
msgid "Specify arbitrary DNS resource record"
msgstr "Spezifiziere einen beliebiegen DNS Eintrag"

#: option.c:538
msgid "Bind to interfaces in use - check for new interfaces"
msgstr "Bindung an Schnittstellen in Benutzung - prüfe auf neue Schnittstellen"

#: option.c:539
msgid "Export local names to global DNS"
msgstr "Exportiere lokale Namen in das globale DNS"

#: option.c:540
msgid "Domain to export to global DNS"
msgstr "Domain für globales DNS ausgeben"

#: option.c:541
msgid "Set TTL for authoritative replies"
msgstr "Setze Gültigkeitsdauer für autoritative Antworten"

#: option.c:542
msgid "Set authoritative zone information"
msgstr "Setze autoritative Zoneninformationen"

#: option.c:543
msgid "Secondary authoritative nameservers for forward domains"
msgstr "Sekundärer autoritativer Nameserver für weitergeleitete Domains"

#: option.c:544
msgid "Peers which are allowed to do zone transfer"
msgstr "Peers, die einen Zonentransfer durchführen dürfen"

#: option.c:545
msgid "Specify ipsets to which matching domains should be added"
msgstr "Spezifiziere IPSets, zu denen passende Domains hinzugefügt werden sollen"

#: option.c:546
msgid "Specify nftables sets to which matching domains should be added"
msgstr "Geben Sie nftables sets an, zu denen passende Domänen hinzugefügt werden sollen"

#: option.c:547
msgid "Enable filtering of DNS queries with connection-track marks."
msgstr "Aktivieren Sie das Filtern von DNS-Abfragen mit \"connection-track\"-Markierungen."

#: option.c:548
msgid "Set allowed DNS patterns for a connection-track mark."
msgstr "Legen Sie zulässige DNS-Muster für eine \"connection-track\"-Markierung fest."

#: option.c:549
msgid "Specify a domain and address range for synthesised names"
msgstr "Spezifiziere eine Domain und Adressbereich für synthetisierte Namen"

#: option.c:550
msgid "Activate DNSSEC validation"
msgstr "Aktiviere DNSSEC-Validierung"

#: option.c:551
msgid "Specify trust anchor key digest."
msgstr "Spezifiziere Vertrauensursprung (Trust Anchor) der Schlüssel-Prüfdaten (Key Digest)."

# while there is no German manual, mark "not for productive use"
#: option.c:552
msgid "Disable upstream checking for DNSSEC debugging."
msgstr "Deaktiviere die vorgelagerte Prüfung für die DNS-Fehlersuche. (Nicht für produktiven Einsatz!)"

#: option.c:553
msgid "Ensure answers without DNSSEC are in unsigned zones."
msgstr "Stellt sicher, dass Antworten ohne DNSSEC sich in einer unsignierten Zone befinden."

#: option.c:554
msgid "Don't check DNSSEC signature timestamps until first cache-reload"
msgstr "DNSSEC Signatur-Zeitstempel nicht prüfen, bis erstmalig der Cache neugeladen wird"

#: option.c:555
msgid "Timestamp file to verify system clock for DNSSEC"
msgstr "Zeitstempel-Datei für die Verifizierung der Systemuhrzeit für DNSSEC"

#: option.c:556
msgid "Set MTU, priority, resend-interval and router-lifetime"
msgstr "Setze MTU, Priorität, Sendewiederholintervall und Router-Lebensdauer"

#: option.c:557
msgid "Do not log routine DHCP."
msgstr "Protokolliere kein Routine-DHCP."

#: option.c:558
msgid "Do not log routine DHCPv6."
msgstr "Protokolliere kein Routine-DHCPv6."

#: option.c:559
msgid "Do not log RA."
msgstr "RA nicht protokollieren."

#: option.c:560
msgid "Log debugging information."
msgstr "Debug-(Fehlersuch-)Information protokollieren."

#: option.c:561
msgid "Accept queries only from directly-connected networks."
msgstr "Akzeptiere nur Anfragen von direkt verbundenen Netzwerken."

#: option.c:562
msgid "Detect and remove DNS forwarding loops."
msgstr "Erkennen und Entfernen von DNS-Weiterleitungsschleifen."

#: option.c:563
msgid "Ignore DNS responses containing ipaddr."
msgstr "Ignoriere DNS-Antworten, die ipaddr enthalten."

#: option.c:564
msgid "Set TTL in DNS responses with DHCP-derived addresses."
msgstr "Setze TTL in DNS-Antworten mit DHCP-abgeleiteten Adressen."

#: option.c:565
msgid "Delay DHCP replies for at least number of seconds."
msgstr "Verzögere DHCP-Antworten mindestens für gegebene Anzahl von Sekunden."

#: option.c:566
msgid "Enables DHCPv4 Rapid Commit option."
msgstr "Aktiviert die DHCPv4-\"Rapid Commit\"-Option."

#: option.c:567
msgid "Path to debug packet dump file"
msgstr "Pfad zur Paketablagedatei zur Fehlersuche"

#: option.c:568
msgid "Mask which packets to dump"
msgstr "Maskiere Pakete, welche abgelegt werden sollen"

#: option.c:569
msgid "Call dhcp-script when lease expiry changes."
msgstr "Rufe dhcp-script auf, wenn der Ablauf des Leases sich ändert."

#: option.c:570
msgid "Send Cisco Umbrella identifiers including remote IP."
msgstr "Senden Sie Cisco Umbrella-Identifikatoren einschließlich der Remote-IP."

#: option.c:571
msgid "Do not log routine TFTP."
msgstr "Protokolliere kein Routine-TFTP."

#: option.c:572
msgid "Suppress round-robin ordering of DNS records."
msgstr "Unterdrückt die Round-Robin-Sortierung von DNS-Einträgen."

#: option.c:802
#, c-format
msgid ""
"Usage: dnsmasq [options]\n"
"\n"
msgstr ""
"Verwendung: dnsmasq [Optionen]\n"
"\n"

#: option.c:804
#, c-format
msgid "Use short options only on the command line.\n"
msgstr "Auf der Befehlszeile nur kurze Optionen verwenden!\n"

#: option.c:806
#, c-format
msgid "Valid options are:\n"
msgstr "Gültige Optionen sind:\n"

#: option.c:853 option.c:1055
msgid "bad address"
msgstr "Fehlerhafte Adresse"

#: option.c:882 option.c:886
msgid "bad port"
msgstr "Fehlerhafter Port"

#: option.c:899 option.c:1002 option.c:1048
msgid "interface binding not supported"
msgstr "Schnittstellenbindung nicht unterstützt"

#: option.c:955
msgid "Cannot resolve server name"
msgstr "Servername kann nicht aufgelöst werden"

#: option.c:991
msgid "cannot use IPv4 server address with IPv6 source address"
msgstr "IPv4-Serveradresse kann nicht mit IPv6-Quelladresse verwendet werden"

#: option.c:997 option.c:1043
msgid "interface can only be specified once"
msgstr "Schnittstelle kann nur einmal angegeben werden"

#: option.c:1011 option.c:4785
msgid "bad interface name"
msgstr "Fehlerhafter Schnittestellenname"

#: option.c:1037
msgid "cannot use IPv6 server address with IPv4 source address"
msgstr "IPv6-Serveradresse kann nicht mit IPv4-Quelladresse verwendet werden"

#: option.c:1124
msgid "bad IPv4 prefix length"
msgstr "ungültige IPv4-Präfixlänge"

#: option.c:1155 option.c:1165 option.c:1240 option.c:1250 option.c:5360
msgid "error"
msgstr "Fehler"

#: option.c:1207
msgid "bad IPv6 prefix length"
msgstr "ungültige IPv6-Präfixlänge"

#: option.c:1467
msgid "inappropriate vendor:"
msgstr "Ungeeigneter Anbieter:"

#: option.c:1474
msgid "inappropriate encap:"
msgstr "Ungeeignete Kapselung:"

#: option.c:1500
msgid "unsupported encapsulation for IPv6 option"
msgstr "Nicht unterstützte Verkapselung für eine IPv6-Option"

#: option.c:1514
msgid "bad dhcp-option"
msgstr "Fehlerhafte DHCP-Option"

#: option.c:1592
msgid "bad IP address"
msgstr "Fehlerhafte IP-Adresse"

#: option.c:1595 option.c:1734 option.c:3928
msgid "bad IPv6 address"
msgstr "Fehlerhafte IPv6-Adresse"

#: option.c:1688
msgid "bad IPv4 address"
msgstr "Fehlerhafte IPv4-Adresse"

#: option.c:1761 option.c:1856
msgid "bad domain in dhcp-option"
msgstr "Fehlerhafte Domäne in DHCP-Option"

#: option.c:1900
msgid "dhcp-option too long"
msgstr "DHCP-Option zu lang"

#: option.c:1907
msgid "illegal dhcp-match"
msgstr "Unzulässige dhcp-match-Option"

#: option.c:1966
msgid "illegal repeated flag"
msgstr "unzulässig wiederholte Markierung"

#: option.c:1974
msgid "illegal repeated keyword"
msgstr "unzulässig wiederholtes Schlüsselwort"

#: option.c:2056 option.c:5533
#, c-format
msgid "cannot access directory %s: %s"
msgstr "Kann auf Verzeichnis %s nicht zugreifen: %s"

#: option.c:2102 tftp.c:573 dump.c:72
#, c-format
msgid "cannot access %s: %s"
msgstr "Kann auf %s nicht zugreifen: %s"

#: option.c:2219
msgid "setting log facility is not possible under Android"
msgstr "Die Einstellung der \"log facility\" kann unter Android nicht gesetzt werden"

#: option.c:2228
msgid "bad log facility"
msgstr "Falsche \"log facility\""

#: option.c:2281
msgid "bad MX preference"
msgstr "fehlerhafte MX-Präferenz-Angabe"

#: option.c:2289
msgid "bad MX name"
msgstr "fehlerhafter MX-Name"

#: option.c:2304
msgid "bad MX target"
msgstr "fehlerhaftes MX-Ziel"

#: option.c:2324
msgid "recompile with HAVE_SCRIPT defined to enable lease-change scripts"
msgstr "Neuübersetzung mit HAVE_SCRIPT nötig, um Lease-Änderungs-Skripte auszuführen"

#: option.c:2328
msgid "recompile with HAVE_LUASCRIPT defined to enable Lua scripts"
msgstr "Neuübersetzung mit HAVE_LUASCRIPT nötig, um benutzerdefinierte Lua-Skripte auszuführen"

#: option.c:2447
msgid "invalid auth-zone"
msgstr "unzulässiger Alias-Bereich"

#: option.c:2589 option.c:2621
msgid "bad prefix length"
msgstr "fehlerhafte Präfixlänge"

#: option.c:2601 option.c:2642 option.c:2696
msgid "bad prefix"
msgstr "fehlerhaftes Präfix"

#: option.c:2716
msgid "prefix length too small"
msgstr "Präfixlänge ist zu klein"

#: option.c:3010
msgid "Bad address in --address"
msgstr "Fehlerhafte Adresse in --address"

#: option.c:3110
msgid "recompile with HAVE_IPSET defined to enable ipset directives"
msgstr "Neuübersetzung mit HAVE_IPSET nötig, um IPSet-Direktiven zu aktivieren"

#: option.c:3117
msgid "recompile with HAVE_NFTSET defined to enable nftset directives"
msgstr "Neukompilieren mit definiertem HAVE_NFTSET, um NFTSET-Direktiven zu aktivieren"

#: option.c:3192 option.c:3210
msgid "recompile with HAVE_CONNTRACK defined to enable connmark-allowlist directives"
msgstr "Neukompilierung mit HAVE_CONNTRACK notwendig, um connmark-allowlist-Direktiven zu aktivieren"

#: option.c:3496
msgid "bad port range"
msgstr "falscher Portbereich"

#: option.c:3522
msgid "bad bridge-interface"
msgstr "fehlerhafte Brücken-Schnittstelle"

#: option.c:3566
msgid "bad shared-network"
msgstr "fehlerhaftes geteiltes Netz (shared-network)"

#: option.c:3620
msgid "only one tag allowed"
msgstr "nur eine Marke zulässig"

#: option.c:3641 option.c:3657 option.c:3783 option.c:3791 option.c:3834
msgid "bad dhcp-range"
msgstr "fehlerhafter DHCP-Bereich"

#: option.c:3675
msgid "inconsistent DHCP range"
msgstr "inkonsistenter DHCP-Bereich"

#: option.c:3741
msgid "prefix length must be exactly 64 for RA subnets"
msgstr "Die Präfixlänge für RA-Subnetze muss genau 64 sein"

#: option.c:3743
msgid "prefix length must be exactly 64 for subnet constructors"
msgstr "Die Präfixlänge für Subnet-Konstruktor muss genau 64 sein"

#: option.c:3746
msgid "prefix length must be at least 64"
msgstr "Die Präfixlänge muss mindestens 64 sein"

#: option.c:3749
msgid "inconsistent DHCPv6 range"
msgstr "Inkonsistenter DHCPv6-Bereich"

#: option.c:3768
msgid "prefix must be zero with \"constructor:\" argument"
msgstr "Präfix muss in Verbindung mit \"constructor:\" Argument Null sein"

#: option.c:3893 option.c:3971
msgid "bad hex constant"
msgstr "Fehlerhafte Hex-Konstante"

#: option.c:3946
msgid "bad IPv6 prefix"
msgstr "fehlerhaftes IPv6-Präfix"

#: option.c:3994
#, c-format
msgid "duplicate dhcp-host IP address %s"
msgstr "doppelte dhcp-host IP-Adresse %s"

#: option.c:4056
msgid "bad DHCP host name"
msgstr "fehlerhafter DHCP-Hostname"

#: option.c:4142
msgid "bad tag-if"
msgstr "fehlerhafte bedingte Marke (tag-if)"

#: option.c:4490 option.c:5046
msgid "invalid port number"
msgstr "unzulässige Portnummer"

#: option.c:4546
msgid "bad dhcp-proxy address"
msgstr "Fehlerhafte DHCP-Proxy-Adresse"

#: option.c:4627
msgid "Bad dhcp-relay"
msgstr "Unzulässiges \"dhcp-relay\""

#: option.c:4671
msgid "bad RA-params"
msgstr "fehlerhafte RA-Parameter"

#: option.c:4681
msgid "bad DUID"
msgstr "fehlerhafte DUID"

#: option.c:4715
msgid "missing address in alias"
msgstr "Adresse fehlt in Alias"

#: option.c:4721
msgid "invalid alias range"
msgstr "unzulässiger Alias-Bereich"

#: option.c:4770
msgid "missing address in dynamic host"
msgstr "Adresse fehlt in dynamischem Host"

#: option.c:4785
msgid "bad dynamic host"
msgstr "fehlerhafter dynamischer Host"

#: option.c:4803 option.c:4819
msgid "bad CNAME"
msgstr "fehlerhafter CNAME"

#: option.c:4827
msgid "duplicate CNAME"
msgstr "doppelter CNAME"

#: option.c:4854
msgid "bad PTR record"
msgstr "fehlerhafter PTR-Eintrag"

#: option.c:4889
msgid "bad NAPTR record"
msgstr "fehlerhafter NAPTR-Eintrag"

#: option.c:4925
msgid "bad RR record"
msgstr "fehlerhafter RR-Eintrag"

#: option.c:4958
msgid "bad CAA record"
msgstr "fehlerhafter CAA-Eintrag"

#: option.c:4987
msgid "bad TXT record"
msgstr "fehlerhafter TXT-Eintrag"

#: option.c:5030
msgid "bad SRV record"
msgstr "fehlerhafter SRV-Eintrag"

#: option.c:5037
msgid "bad SRV target"
msgstr "fehlerhaftes SRV-Ziel"

#: option.c:5056
msgid "invalid priority"
msgstr "unzulässige Priorität"

#: option.c:5061
msgid "invalid weight"
msgstr "unzulässige Wichtung"

#: option.c:5084
msgid "Bad host-record"
msgstr "Fehlerhafter \"host-record\""

#: option.c:5123
msgid "Bad name in host-record"
msgstr "Fehlerhafter Name in \"host-record\""

#: option.c:5165
msgid "bad value for dnssec-check-unsigned"
msgstr "Fehlerhafter Wert für \"dnssec-check-unsigned\""

#: option.c:5201
msgid "bad trust anchor"
msgstr "fehlerhafter Vertrauensursprung (Trust Anchor)"

#: option.c:5217
msgid "bad HEX in trust anchor"
msgstr "fehlerhafter Hexwert in Vertrauensursprung (Trust Anchor)"

#: option.c:5228
msgid "unsupported option (check that dnsmasq was compiled with DHCP/TFTP/DNSSEC/DBus support)"
msgstr "Nicht unterstützte Option (prüfen Sie, ob DNSMasq mit DHCP/TFTP/DNSSEC/DBus-Unterstützung übersetzt wurde)"

#: option.c:5290
msgid "missing \""
msgstr "fehlende \\\""

#: option.c:5347
msgid "bad option"
msgstr "fehlerhafter Option"

#: option.c:5349
msgid "extraneous parameter"
msgstr "überschüssiger Parameter"

#: option.c:5351
msgid "missing parameter"
msgstr "fehlender Parameter"

#: option.c:5353
msgid "illegal option"
msgstr "unzulässige Option"

#: option.c:5363
#, c-format
msgid " in output from %s"
msgstr " in der Ausgabe von %s"

#: option.c:5365
#, c-format
msgid " at line %d of %s"
msgstr " in Zeile %d von %s"

#: option.c:5380 option.c:5683 option.c:5694
#, c-format
msgid "read %s"
msgstr "%s gelesen"

#: option.c:5446
#, c-format
msgid "cannot execute %s: %s"
msgstr "kann %s nicht ausführen: %s"

#: option.c:5454 option.c:5615 tftp.c:790
#, c-format
msgid "cannot read %s: %s"
msgstr "kann %s nicht lesen: %s"

#: option.c:5473
#, c-format
msgid "error executing %s: %s"
msgstr "Fehler bei der Ausführung von %s: %s"

#: option.c:5476
#, c-format
msgid "%s returns non-zero error code"
msgstr "%s gibt einen Fehlercode ungleich Null zurück"

#: option.c:5775
msgid "junk found in command line"
msgstr "Müll in der Kommandozeile gefunden"

#: option.c:5815
#, c-format
msgid "Dnsmasq version %s  %s\n"
msgstr "Dnsmasq Version %s  %s\n"

#: option.c:5816
#, c-format
msgid ""
"Compile time options: %s\n"
"\n"
msgstr ""
"Kompilierungs-Optionen %s\n"
"\n"

#: option.c:5817
#, c-format
msgid "This software comes with ABSOLUTELY NO WARRANTY.\n"
msgstr "Für diese Software wird ABSOLUT KEINE GARANTIE gewährt.\n"

# FIXME: this must be one long string! -- MA
#: option.c:5818
#, c-format
msgid "Dnsmasq is free software, and you are welcome to redistribute it\n"
msgstr "Dnsmasq ist freie Software, und darf unter den Bedingungen der\n"

#: option.c:5819
#, c-format
msgid "under the terms of the GNU General Public License, version 2 or 3.\n"
msgstr "GNU General Public Lizenz, Version 2 oder 3, weiterverteilt werden.\n"

#: option.c:5836
msgid "try --help"
msgstr "versuchen Sie --help"

#: option.c:5838
msgid "try -w"
msgstr "versuchen Sie -w"

#: option.c:5840
#, c-format
msgid "bad command line options: %s"
msgstr "fehlerhafte Optionen auf der Befehlszeile: %s"

#: option.c:5909
#, c-format
msgid "CNAME loop involving %s"
msgstr "CNAME-Schleife mit %s"

#: option.c:5950
#, c-format
msgid "cannot get host-name: %s"
msgstr "kann Hostnamen nicht ermitteln: %s"

#: option.c:5978
msgid "only one resolv.conf file allowed in no-poll mode."
msgstr "mit -n/--no-poll ist nur eine resolv.conf-Datei zulässig."

#: option.c:5988
msgid "must have exactly one resolv.conf to read domain from."
msgstr "muss genau eine resolv.conf-Datei haben, um die Domäne zu lesen."

#: option.c:5991 network.c:1727 dhcp.c:892
#, c-format
msgid "failed to read %s: %s"
msgstr "konnte %s nicht lesen: %s"

#: option.c:6008
#, c-format
msgid "no search directive found in %s"
msgstr "keine \"search\"-Anweisung in %s gefunden"

#: option.c:6029
msgid "there must be a default domain when --dhcp-fqdn is set"
msgstr "Es muss eine Standard-Domain gesetzt sein, wenn --dhcp-fqdn gesetzt ist"

#: option.c:6038
msgid "syntax check OK"
msgstr "Syntaxprüfung OK"

#: forward.c:107
#, c-format
msgid "failed to send packet: %s"
msgstr "Paketversand gescheitert: %s"

#: forward.c:715
msgid "discarding DNS reply: subnet option mismatch"
msgstr "Verwerfe DNS Antwort: Subnetoption stimmt nicht überrein"

#: forward.c:780
#, c-format
msgid "nameserver %s refused to do a recursive query"
msgstr "Namensserver %s hat eine rekursive Anfrage verweigert"

#: forward.c:826
#, c-format
msgid "possible DNS-rebind attack detected: %s"
msgstr "möglichen DNS-Rebind-Angriff entdeckt: %s"

#: forward.c:1239
#, c-format
msgid "reducing DNS packet size for nameserver %s to %d"
msgstr "Reduziere die DNS-Paketgröße für Nameserver %s auf %d"

#: forward.c:1565
#, c-format
msgid "ignoring query from non-local network %s (logged only once)"
msgstr "Ignoriere Abfrage von nicht-lokalen Netzwerk %s (Nur einmal protokolliert)"

#: forward.c:2139
#, c-format
msgid "ignoring query from non-local network %s"
msgstr "Ignoriere Abfrage von nicht-lokalen Netzwerk %s"

#: forward.c:2501
#, c-format
msgid "failed to bind server socket to %s: %s"
msgstr "konnte nicht an Server-Socket für %s binden: %s"

#: forward.c:2867
#, c-format
msgid "Maximum number of concurrent DNS queries reached (max: %d)"
msgstr "Höchstzahl an nebenläufiger DNS-Anfragen erreicht (max. %d)"

#: forward.c:2869
#, c-format
msgid "Maximum number of concurrent DNS queries to %s reached (max: %d)"
msgstr "Maximale Anzahl gleichzeitiger DNS-Abfragen, die erreicht %s (max. %d)"

#: network.c:700
#, c-format
msgid "stopped listening on %s(#%d): %s port %d"
msgstr "Empfang auf %s(#%d) beendet: %s Port %d"

#: network.c:911
#, c-format
msgid "failed to create listening socket for %s: %s"
msgstr "Konnte Empfangs-Socket für %s nicht erzeugen: %s"

#: network.c:1192
#, c-format
msgid "listening on %s(#%d): %s port %d"
msgstr "Empfang auf %s(#%d): %s Port %d"

#: network.c:1219
#, c-format
msgid "listening on %s port %d"
msgstr "Empfang auf %s Port %d"

#: network.c:1252
#, c-format
msgid "LOUD WARNING: listening on %s may accept requests via interfaces other than %s"
msgstr "LOUD WARNING: Empfang auf %s kann die Anfragen auf anderen Schnittstellen als %s akzeptieren"

#: network.c:1259
msgid "LOUD WARNING: use --bind-dynamic rather than --bind-interfaces to avoid DNS amplification attacks via these interface(s)"
msgstr "LOUD WARNING: Es sollte --bind-dynamic anstatt --bind-interfaces benutzt werden, um DNS-Verstärkungsangriffe auf diesen Schnittstellen zu unterbinden"

#: network.c:1268
#, c-format
msgid "warning: using interface %s instead"
msgstr "Warnung: benutze stattdessen Schnittstelle %s"

#: network.c:1277
#, c-format
msgid "warning: no addresses found for interface %s"
msgstr "Warnung: keine Adressen für die Schnittstelle %s gefunden"

#: network.c:1335
#, c-format
msgid "interface %s failed to join DHCPv6 multicast group: %s"
msgstr "Schnittstelle %s konnte DHCPv6-Multicast-Gruppe nicht beitreten: %s"

#: network.c:1340
msgid "try increasing /proc/sys/net/core/optmem_max"
msgstr "Versuchen Sie, /proc/sys/net/core/optmem_max zu erhöhen"

#: network.c:1545
#, c-format
msgid "failed to bind server socket for %s: %s"
msgstr "konnte nicht an Server-Socket für %s binden: %s"

#: network.c:1622
#, c-format
msgid "ignoring nameserver %s - local interface"
msgstr "ignoriere Namensserver %s - lokale Schnittstelle"

#: network.c:1633
#, c-format
msgid "ignoring nameserver %s - cannot make/bind socket: %s"
msgstr "ignoriere Namensserver %s - kann Socket nicht erzeugen/binden: %s"

#: network.c:1643
msgid "more servers are defined but not logged"
msgstr "mehr Server sind definiert aber nicht protokolliert"

#: network.c:1654
msgid "(no DNSSEC)"
msgstr "(kein DNSSEC)"

# FIXME: this isn't translatable - always provide full strings, do not assemble yourself! -- MA
#: network.c:1657
msgid "unqualified"
msgstr "unqualifizierte"

#: network.c:1657
msgid "names"
msgstr "Namen"

#: network.c:1659
msgid "default"
msgstr "Standard"

#: network.c:1661
msgid "domain"
msgstr "Domäne"

#: network.c:1663
#, c-format
msgid "using nameserver %s#%d for %s %s%s %s"
msgstr "benutze Namensserver %s#%d für %s %s%s %s"

#: network.c:1667
#, c-format
msgid "NOT using nameserver %s#%d - query loop detected"
msgstr "Benutze Namensserver %s#%d NICHT - Anfragenschleife festgetellt"

#: network.c:1670
#, c-format
msgid "using nameserver %s#%d(via %s)"
msgstr "Benutze Namensserver %s#%d(via %s)"

#: network.c:1672
#, c-format
msgid "using nameserver %s#%d"
msgstr "Benutze Namensserver %s#%d"

#: network.c:1687
#, c-format
msgid "using only locally-known addresses for %s"
msgstr "benutze nur lokal-bekannte Adressen für %s"

#: network.c:1690
#, c-format
msgid "using standard nameservers for %s"
msgstr "benutze standard Namensserver für %s"

#: network.c:1694
#, c-format
msgid "using %d more local addresses"
msgstr "Benutze weitere %d lokale Adressen"

#: network.c:1696
#, c-format
msgid "using %d more nameservers"
msgstr "Benutze weitere %d Namensserver"

#: dnsmasq.c:192
msgid "dhcp-hostsdir, dhcp-optsdir and hostsdir are not supported on this platform"
msgstr "dhcp-hostsdir, dhcp-optsdir und hostsdir werden auf dieser Plattform nicht unterstüzt"

#: dnsmasq.c:207
msgid "no root trust anchor provided for DNSSEC"
msgstr "Keine Root-Vertrauensanker (Root Trust Anchor) für DNSSEC verfügbar"

#: dnsmasq.c:210
msgid "cannot reduce cache size from default when DNSSEC enabled"
msgstr "Kann die Standard-Zwischenspeichergröße nicht verkleinern, wenn DNSSEC aktiviert ist"

#: dnsmasq.c:212
msgid "DNSSEC not available: set HAVE_DNSSEC in src/config.h"
msgstr "DNSSEC nicht verfügbar: setzen Sie HAVE_DNSSEC in src/config.h"

#: dnsmasq.c:218
msgid "TFTP server not available: set HAVE_TFTP in src/config.h"
msgstr "TFTP-Server nicht verfügbar, setzen Sie HAVE_TFTP in src/config.h"

#: dnsmasq.c:225
msgid "cannot use --conntrack AND --query-port"
msgstr "Kann nicht --conntrack UND --query-port einsetzen"

#: dnsmasq.c:231
msgid "conntrack support not available: set HAVE_CONNTRACK in src/config.h"
msgstr "Conntrack-Unterstützung nicht verfügbar: Aktivieren Sie HAVE_CONNTRACK in src/config.h"

#: dnsmasq.c:236
msgid "asynchronous logging is not available under Solaris"
msgstr "asynchrone Protokollierung ist unter Solaris nicht verfügbar"

#: dnsmasq.c:241
msgid "asynchronous logging is not available under Android"
msgstr "Asynchrone Protokollierung ist unter Android nicht verfügbar"

#: dnsmasq.c:246
msgid "authoritative DNS not available: set HAVE_AUTH in src/config.h"
msgstr "Autoritatives DNS nicht verfügbar: Setzen Sie HAVE_AUTH in src/config.h"

#: dnsmasq.c:251
msgid "loop detection not available: set HAVE_LOOP in src/config.h"
msgstr "Loop-Erkennung nicht verfügbar, aktivieren Sie HAVE_LOOP in src/config.h"

#: dnsmasq.c:256
msgid "Ubus not available: set HAVE_UBUS in src/config.h"
msgstr "UBus nicht verfügbar: setzen Sie HAVE_UBUS in src/config.h"

#: dnsmasq.c:267
msgid "max_port cannot be smaller than min_port"
msgstr "max_port darf nicht kleiner als min_port sein"

#: dnsmasq.c:271
msgid "port_limit must not be larger than available port range"
msgstr "port_limit darf nicht größer als der verfügbare Portbereich sein"

#: dnsmasq.c:278
msgid "--auth-server required when an auth zone is defined."
msgstr "--auth-server ist notwendig, wenn eine Auth-Zone definiert ist."

#: dnsmasq.c:283
msgid "zone serial must be configured in --auth-soa"
msgstr "Seriennummer der Zone muss mit --auth-soa konfiguriert werden"

#: dnsmasq.c:303
msgid "dhcp-range constructor not available on this platform"
msgstr "dhcp-range-Konstruktor ist auf dieser Plattform nicht verfügbar"

#: dnsmasq.c:377
msgid "cannot set --bind-interfaces and --bind-dynamic"
msgstr "Kann nicht --bind-interfaces und --bind-dynamic setzen"

#: dnsmasq.c:380
#, c-format
msgid "failed to find list of interfaces: %s"
msgstr "konnte Schnitstellenliste nicht auffinden: %s"

#: dnsmasq.c:389
#, c-format
msgid "unknown interface %s"
msgstr "unbekannte Schnittstelle %s"

#: dnsmasq.c:396
#, c-format
msgid "failed to set SO_BINDTODEVICE on DHCP socket: %s"
msgstr "kann SO_BINDTODEVICE für DHCP-Socket nicht aktivieren: %s"

#: dnsmasq.c:440
msgid "Packet dumps not available: set HAVE_DUMP in src/config.h"
msgstr "Paketmitschnitt nicht verfügbar: setzen Sie HAVE_DUMP in src/config.h"

#: dnsmasq.c:448 dnsmasq.c:1232
#, c-format
msgid "DBus error: %s"
msgstr "DBus-Fehler: %s"

#: dnsmasq.c:451
msgid "DBus not available: set HAVE_DBUS in src/config.h"
msgstr "DBus nicht verfügbar: setzen Sie HAVE_DBUS in src/config.h"

#: dnsmasq.c:459 dnsmasq.c:1253
#, c-format
msgid "UBus error: %s"
msgstr "UBus-Fehler: %s"

#: dnsmasq.c:462
msgid "UBus not available: set HAVE_UBUS in src/config.h"
msgstr "UBus nicht verfügbar: setzen Sie HAVE_UBUS in src/config.h"

#: dnsmasq.c:492
#, c-format
msgid "unknown user or group: %s"
msgstr "Unbekannter Benutzer oder Gruppe: %s"

#: dnsmasq.c:568
#, c-format
msgid "process is missing required capability %s"
msgstr "Prozess benötigt verlangte Fähigkeit %s"

#: dnsmasq.c:600
#, c-format
msgid "cannot chdir to filesystem root: %s"
msgstr "kann nicht ins Wurzelverzeichnis des Dateisystems wechseln: %s"

# FIXME: this and the next would need commas after the version
#: dnsmasq.c:852
#, c-format
msgid "started, version %s DNS disabled"
msgstr "gestartet, Version %s, DNS abgeschaltet"

#: dnsmasq.c:857
#, c-format
msgid "started, version %s cachesize %d"
msgstr "gestartet, Version %s, Zwischenspeichergröße %d"

#: dnsmasq.c:859
msgid "cache size greater than 10000 may cause performance issues, and is unlikely to be useful."
msgstr "Eine Cachegröße größer als 10000 kann Performanceprobleme verursachen und Nutzen ist wenig wahrscheinlich."

#: dnsmasq.c:862
#, c-format
msgid "started, version %s cache disabled"
msgstr "Gestartet, Version %s, Zwischenspeicher deaktiviert"

#: dnsmasq.c:865
msgid "DNS service limited to local subnets"
msgstr "DNS-Dienst auf Unternetze eingeschränkt"

#: dnsmasq.c:868
#, c-format
msgid "compile time options: %s"
msgstr "Optionen bei Übersetzung: %s"

#: dnsmasq.c:877
msgid "DBus support enabled: connected to system bus"
msgstr "DBus-Unterstützung eingeschaltet: mit Systembus verbunden"

#: dnsmasq.c:879
msgid "DBus support enabled: bus connection pending"
msgstr "DBus-Unterstützung eingeschaltet: warte auf Systembus-Verbindung"

#: dnsmasq.c:887
msgid "UBus support enabled: connected to system bus"
msgstr "UBus-Unterstützung aktiviert: mit Systembus verbunden"

#: dnsmasq.c:889
msgid "UBus support enabled: bus connection pending"
msgstr "UBus-Unterstützung aktiviert: Bus-Verbindung wird hergestellt"

#: dnsmasq.c:909
msgid "DNSSEC validation enabled but all unsigned answers are trusted"
msgstr "DNSSEC-Validierung aktiviert, jedoch wird allen unsignierten Antworten vertraut"

#: dnsmasq.c:911
msgid "DNSSEC validation enabled"
msgstr "DNSSEC-Validierung aktiviert"

#: dnsmasq.c:915
msgid "DNSSEC signature timestamps not checked until receipt of SIGINT"
msgstr "DNSSEC-Signatur-Zeitstempel werden erst nach Empfang von SIGINT überprüft"

#: dnsmasq.c:918
msgid "DNSSEC signature timestamps not checked until system time valid"
msgstr "DNSSEC Signatur-Zeitstempel werden erst überprüft, sobald die Systemuhrzeit gültig ist"

#: dnsmasq.c:921
#, c-format
msgid "configured with trust anchor for %s keytag %u"
msgstr "konfiguriert mit Vertrauensanker für %s Schlüsselanhänger %u"

#: dnsmasq.c:927
#, c-format
msgid "warning: failed to change owner of %s: %s"
msgstr "Warnung: konnte den Besitzer von %s nicht ändern: %s"

#: dnsmasq.c:932
msgid "setting --bind-interfaces option because of OS limitations"
msgstr "Aktiviere --bind-interfaces wegen Einschränkungen des Betriebssystems"

#: dnsmasq.c:945
#, c-format
msgid "warning: interface %s does not currently exist"
msgstr "Warnung: Schnittstelle %s existiert derzeit nicht"

#: dnsmasq.c:950
msgid "warning: ignoring resolv-file flag because no-resolv is set"
msgstr "Warnung: Ignoriere \"resolv-file\", weil \"no-resolv\" aktiv ist"

#: dnsmasq.c:953
msgid "warning: no upstream servers configured"
msgstr "Warnung: keine vorgeschalteten Server konfiguriert"

#: dnsmasq.c:957
#, c-format
msgid "asynchronous logging enabled, queue limit is %d messages"
msgstr "asynchrone Protokollierung eingeschaltet, Warteschlange fasst %d Nachrichten"

#: dnsmasq.c:978
msgid "IPv6 router advertisement enabled"
msgstr "IPv6-Router-Advertisement aktiviert"

#: dnsmasq.c:983
#, c-format
msgid "DHCP, sockets bound exclusively to interface %s"
msgstr "DHCP, Sockets exklusiv an die Schnittstelle %s gebunden"

# FIXME: this and the next few must be full strings to be translatable - do not assemble in code"
#: dnsmasq.c:1000
msgid "root is "
msgstr "Wurzel ist "

#: dnsmasq.c:1000
msgid "enabled"
msgstr "Aktiviert"

#: dnsmasq.c:1002
msgid "secure mode"
msgstr "sicherer Modus"

#: dnsmasq.c:1003
msgid "single port mode"
msgstr "Einzelport-Modus"

#: dnsmasq.c:1006
#, c-format
msgid "warning: %s inaccessible"
msgstr "Warnung: %s unerreichbar"

#: dnsmasq.c:1010
#, c-format
msgid "warning: TFTP directory %s inaccessible"
msgstr "Warnung: Das TFTP-Verzeichnis %s ist unerreichbar"

#: dnsmasq.c:1036
#, c-format
msgid "restricting maximum simultaneous TFTP transfers to %d"
msgstr "Begrenze gleichzeitige TFTP-Übertragungen auf maximal %d"

#: dnsmasq.c:1095
#, c-format
msgid "error binding DHCP socket to device %s"
msgstr "Fehler beim Binden des DHCP-Sockets an Gerät %s"

#: dnsmasq.c:1229
msgid "connected to system DBus"
msgstr "Mit System-DBus verbunden"

#: dnsmasq.c:1250
msgid "connected to system UBus"
msgstr "mit System-UBus verbunden"

#: dnsmasq.c:1416
#, c-format
msgid "cannot fork into background: %s"
msgstr "kann nicht in den Hintergrund abspalten: %s"

#: dnsmasq.c:1420
#, c-format
msgid "failed to create helper: %s"
msgstr "kann Helfer nicht erzeugen: %s"

#: dnsmasq.c:1424
#, c-format
msgid "setting capabilities failed: %s"
msgstr "kann \"capabilities\" nicht setzen: %s"

#: dnsmasq.c:1428
#, c-format
msgid "failed to change user-id to %s: %s"
msgstr "Kann nicht Benutzerrechte %s annehmen: %s"

#: dnsmasq.c:1432
#, c-format
msgid "failed to change group-id to %s: %s"
msgstr "Kann nicht Gruppenrechte %s annehmen: %s"

#: dnsmasq.c:1436
#, c-format
msgid "failed to open pidfile %s: %s"
msgstr "kann die Prozessidentifikations-(PID)-Datei %s nicht öffnen: %s"

#: dnsmasq.c:1440
#, c-format
msgid "cannot open log %s: %s"
msgstr "Kann Logdatei %s nicht öffnen: %s"

#: dnsmasq.c:1444
#, c-format
msgid "failed to load Lua script: %s"
msgstr "Konnte Lua-Script nicht laden: %s"

#: dnsmasq.c:1448
#, c-format
msgid "TFTP directory %s inaccessible: %s"
msgstr "Das TFTP-Verzeichnis %s ist unerreichbar: %s"

#: dnsmasq.c:1452
#, c-format
msgid "cannot create timestamp file %s: %s"
msgstr "Kann keine Zeitstempel-Datei %s erzeugen: %s"

#: dnsmasq.c:1536
#, c-format
msgid "script process killed by signal %d"
msgstr "Skriptprozess durch Signal %d beendet"

#: dnsmasq.c:1540
#, c-format
msgid "script process exited with status %d"
msgstr "Scriptprozess hat sich mit Status %d beendet"

#: dnsmasq.c:1544
#, c-format
msgid "failed to execute %s: %s"
msgstr "konnte %s nicht ausführen: %s"

#: dnsmasq.c:1584
msgid "now checking DNSSEC signature timestamps"
msgstr "Prüfe jetzt Zeitstempel der DNSSEC-Signaturen"

#: dnsmasq.c:1619 dnssec.c:160 dnssec.c:204
#, c-format
msgid "failed to update mtime on %s: %s"
msgstr "kann die mtime nicht auf %s aktualisieren: %s"

#: dnsmasq.c:1631
msgid "exiting on receipt of SIGTERM"
msgstr "beende nach Empfang von SIGTERM"

#: dnsmasq.c:1659
#, c-format
msgid "failed to access %s: %s"
msgstr "konnte auf %s nicht zugreifen: %s"

#: dnsmasq.c:1690
#, c-format
msgid "reading %s"
msgstr "lese %s"

#: dnsmasq.c:1706
#, c-format
msgid "no servers found in %s, will retry"
msgstr "keine Server in %s gefunden, werde nochmal versuchen"

#: dhcp.c:51
#, c-format
msgid "cannot create DHCP socket: %s"
msgstr "kann DHCP-Socket nicht erzeugen: %s"

#: dhcp.c:66
#, c-format
msgid "failed to set options on DHCP socket: %s"
msgstr "kann Optionen für DHCP-Socket nicht setzen: %s"

#: dhcp.c:87
#, c-format
msgid "failed to set SO_REUSE{ADDR|PORT} on DHCP socket: %s"
msgstr "kann SO_REUSE{ADDR|PORT} für DHCP-Socket nicht aktivieren: %s"

#: dhcp.c:99
#, c-format
msgid "failed to bind DHCP server socket: %s"
msgstr "kann nicht an DHCP-Server-Socket binden: %s"

#: dhcp.c:125
#, c-format
msgid "cannot create ICMP raw socket: %s."
msgstr "kann ICMP-Rohdaten-Socket nicht erzeugen: %s."

#: dhcp.c:254 dhcp6.c:186
#, c-format
msgid "unknown interface %s in bridge-interface"
msgstr "unbekannte Schnittstelle %s in bridge-interface"

#: dhcp.c:295
#, c-format
msgid "DHCP packet received on %s which has no address"
msgstr "DHCP-Paket ohne Adresse an Schnittstelle %s empfangen"

#: dhcp.c:429
#, c-format
msgid "ARP-cache injection failed: %s"
msgstr "Einspeisen in ARP-Zwischenspeicher fehlgeschlagen: %s"

#: dhcp.c:490
#, c-format
msgid "Error sending DHCP packet to %s: %s"
msgstr "Fehler beim Senden des DHCP-Pakets an %s: %s"

#: dhcp.c:547
#, c-format
msgid "DHCP range %s -- %s is not consistent with netmask %s"
msgstr "DHCP-Bereich %s - %s passt nicht zur Netzmaske %s"

#: dhcp.c:930
#, c-format
msgid "bad line at %s line %d"
msgstr "ungültige Zeile %2$d in Datei %1$s"

#: dhcp.c:973
#, c-format
msgid "ignoring %s line %d, duplicate name or IP address"
msgstr "ignoriere %s Zeile %d, doppelter Name oder doppelte IP-Adresse"

#: dhcp.c:1034
#, c-format
msgid "read %s - %d addresses"
msgstr "%s gelesen - %d Adressen"

#: dhcp.c:1136
#, c-format
msgid "Cannot broadcast DHCP relay via interface %s"
msgstr "DHCP-Relay kann nicht über die Schnittstelle %s gesendet werden"

#: dhcp.c:1160
#, c-format
msgid "broadcast via %s"
msgstr "broadcast via %s"

#: dhcp.c:1163 rfc3315.c:2219
#, c-format
msgid "DHCP relay at %s -> %s"
msgstr "DHCP-Relay bei %s -> %s"

#: lease.c:64
#, c-format
msgid "ignoring invalid line in lease database: %s %s %s %s ..."
msgstr "ignoriere ungültige Zeile in Lease-Datenbank: %s %s %s %s ..."

#: lease.c:101
#, c-format
msgid "ignoring invalid line in lease database, bad address: %s"
msgstr "ignoriere ungültige Zeile in Lease-Datenbank, fehlerhafte Adresse: %s"

#: lease.c:108
msgid "too many stored leases"
msgstr "zu viele Leases gespeichert"

#: lease.c:176
#, c-format
msgid "cannot open or create lease file %s: %s"
msgstr "kann Lease-Datei %s nicht öffnen oder anlegen: %s"

#: lease.c:185
msgid "failed to parse lease database cleanly"
msgstr "sauberes Aufgliedern der Lease-Datenbank fehlgeschlagen"

#: lease.c:188
#, c-format
msgid "failed to read lease file %s: %s"
msgstr "konnte Lease-Datei %s nicht lesen: %s"

#: lease.c:204
#, c-format
msgid "cannot run lease-init script %s: %s"
msgstr "kann lease-init-Skript %s nicht ausführen: %s"

#: lease.c:210
#, c-format
msgid "lease-init script returned exit code %s"
msgstr "lease-init-Skript beendete sich mit Code %s"

#: lease.c:381
#, c-format
msgid "failed to write %s: %s (retry in %u s)"
msgstr "Konnte %s nicht schreiben: %s (Neuversuch in %u s)"

#: lease.c:955
#, c-format
msgid "Ignoring domain %s for DHCP host name %s"
msgstr "Ignoriere Domäne %s für DHCP-Hostnamen %s"

#: rfc2131.c:378
msgid "with subnet selector"
msgstr "mit Subnetz-Wähler"

#: rfc2131.c:383
msgid "via"
msgstr "via"

# FIXME: this and the next few are not translatable. Please provide full
# strings, do not programmatically assemble them.
#: rfc2131.c:389
#, c-format
msgid "no address range available for DHCP request %s %s"
msgstr "Kein verfügbarer Adress-Bereich für DHCP-Anfrage %s %s"

#: rfc2131.c:403
#, c-format
msgid "%u available DHCP subnet: %s/%s"
msgstr "%u verfügbares DHCP-Subnetz: %s/%s"

#: rfc2131.c:409 rfc3315.c:320
#, c-format
msgid "%u available DHCP range: %s -- %s"
msgstr "%u verfügbarer DHCP-Bereich: %s - %s"

#: rfc2131.c:521
#, c-format
msgid "%u vendor class: %s"
msgstr "%u \"Vendor class\": %s"

#: rfc2131.c:523
#, c-format
msgid "%u user class: %s"
msgstr "%u Benutzerklasse: %s"

# FIXME: do not programmatically assemble strings - untranslatable
#: rfc2131.c:557
msgid "disabled"
msgstr "deaktiviert"

#: rfc2131.c:598 rfc2131.c:1087 rfc2131.c:1536 rfc3315.c:633 rfc3315.c:816
#: rfc3315.c:1122
msgid "ignored"
msgstr "ignoriert"

#: rfc2131.c:613 rfc2131.c:1340 rfc3315.c:868
msgid "address in use"
msgstr "Adresse in Gebrauch"

#: rfc2131.c:627 rfc2131.c:1141
msgid "no address available"
msgstr "keine Adresse verfügbar"

#: rfc2131.c:634 rfc2131.c:1302
msgid "wrong network"
msgstr "Falsches Netzwerk"

#: rfc2131.c:649
msgid "no address configured"
msgstr "keine Adresse konfiguriert"

#: rfc2131.c:655 rfc2131.c:1353
msgid "no leases left"
msgstr "keine Leases übrig"

#: rfc2131.c:756 rfc3315.c:500
#, c-format
msgid "%u client provides name: %s"
msgstr "%u Klient stellt Name bereit: %s"

#: rfc2131.c:885
msgid "PXE BIS not supported"
msgstr "PXE BIS nicht unterstützt"

#: rfc2131.c:1054 rfc3315.c:1223
#, c-format
msgid "disabling DHCP static address %s for %s"
msgstr "schalte statische DHCP-Adresse %s für %s ab"

# FIXME: do not assemble
#: rfc2131.c:1075
msgid "unknown lease"
msgstr "Unbekannte Lease"

#: rfc2131.c:1110
#, c-format
msgid "not using configured address %s because it is leased to %s"
msgstr "benutze konfigurierte Adresse %s nicht, weil sie an %s verleast ist"

#: rfc2131.c:1120
#, c-format
msgid "not using configured address %s because it is in use by the server or relay"
msgstr "benutze konfigurierte Adresse %s nicht, weil sie von Server/Relais verwendet wird"

#: rfc2131.c:1123
#, c-format
msgid "not using configured address %s because it was previously declined"
msgstr "benutze konfigurierte Adresse %s nicht, weil sie zuvor abgelehnt wurde"

# FIXME: do not assemble
#: rfc2131.c:1139 rfc2131.c:1346
msgid "no unique-id"
msgstr "keine eindeutige ID"

#: rfc2131.c:1238
msgid "wrong server-ID"
msgstr "falsche Server-ID"

#: rfc2131.c:1257
msgid "wrong address"
msgstr "falsche Adresse"

#: rfc2131.c:1275 rfc3315.c:976
msgid "lease not found"
msgstr "Lease nicht gefunden"

#: rfc2131.c:1310
msgid "address not available"
msgstr "Adresse nicht verfügbar"

#: rfc2131.c:1321
msgid "static lease available"
msgstr "Statischer Lease verfügbar"

#: rfc2131.c:1325
msgid "address reserved"
msgstr "Adresse reserviert"

#: rfc2131.c:1334
#, c-format
msgid "abandoning lease to %s of %s"
msgstr "Gebe Lease von %2$s an %1$s auf"

#: rfc2131.c:1870
#, c-format
msgid "%u bootfile name: %s"
msgstr "%u Name der Bootdatei: %s"

#: rfc2131.c:1879
#, c-format
msgid "%u server name: %s"
msgstr "%u Servername: %s"

#: rfc2131.c:1889
#, c-format
msgid "%u next server: %s"
msgstr "%u nächster Server: %s"

#: rfc2131.c:1893
#, c-format
msgid "%u broadcast response"
msgstr "%u Antwort per Broadcast"

#: rfc2131.c:1956
#, c-format
msgid "cannot send DHCP/BOOTP option %d: no space left in packet"
msgstr "kann DHCP/BOOTP-Opition %d nicht setzen: kein Platz mehr im Paket"

#: rfc2131.c:2267
msgid "PXE menu too large"
msgstr "PXE-Menüeintrag zu groß"

#: rfc2131.c:2430 rfc3315.c:1517
#, c-format
msgid "%u requested options: %s"
msgstr "%u angeforderte Optionen: %s"

#: rfc2131.c:2747
#, c-format
msgid "cannot send RFC3925 option: too many options for enterprise number %d"
msgstr "Kann RFC3925-Option nicht senden: zu viele Optionen für Unternehmen Nr. %d"

#: rfc2131.c:2810
#, c-format
msgid "%u reply delay: %d"
msgstr "%u Antwortverzögerung: %d"

#: netlink.c:86
#, c-format
msgid "cannot create netlink socket: %s"
msgstr "kann Netlink-Socket nicht erzeugen: %s"

#: netlink.c:379
#, c-format
msgid "netlink returns error: %s"
msgstr "Netlink liefert Fehler %s"

#: dbus.c:491
#, c-format
msgid "Enabling --%s option from D-Bus"
msgstr "Aktiviere --%s Option von D-Bus"

#: dbus.c:496
#, c-format
msgid "Disabling --%s option from D-Bus"
msgstr "Deaktiviere --%s Option von D-Bus"

#: dbus.c:857
msgid "setting upstream servers from DBus"
msgstr "vorgeschaltete Server von DBus festgelegt"

#: dbus.c:907
msgid "could not register a DBus message handler"
msgstr "konnte Steuerungsprogramm für DBus-Nachrichten nicht anmelden"

#: bpf.c:261
#, c-format
msgid "cannot create DHCP BPF socket: %s"
msgstr "konnte DHCP-BPF-Socket nicht einrichten: %s"

#: bpf.c:289
#, c-format
msgid "DHCP request for unsupported hardware type (%d) received on %s"
msgstr "DHCP-Anfrage für nicht unterstützen Hardwaretyp (%d) auf %s empfangen"

#: bpf.c:374
#, c-format
msgid "cannot create PF_ROUTE socket: %s"
msgstr "Kann PF_ROUTE-Socket nicht erzeugen: %s"

#: bpf.c:395
msgid "Unknown protocol version from route socket"
msgstr "Unbekannte Protokollversion vom Route-Socket"

#: helper.c:150
msgid "lease() function missing in Lua script"
msgstr "lease()-Funktion fehlt im Lua-Skript"

#: tftp.c:353
msgid "unable to get free port for TFTP"
msgstr "konnte keinen freien Port für TFTP bekommen"

#: tftp.c:369
#, c-format
msgid "unsupported request from %s"
msgstr "nicht unterstützte Anfrage von %s"

#: tftp.c:520
#, c-format
msgid "file %s not found for %s"
msgstr "Datei %s nicht gefunden für %s"

#: tftp.c:609
#, c-format
msgid "ignoring packet from %s (TID mismatch)"
msgstr "Paket von %s wird ignoriert (TID-Nichtübereinstimmung)"

#: tftp.c:662
#, c-format
msgid "failed sending %s to %s"
msgstr "konnte %s nicht an %s senden"

#: tftp.c:662
#, c-format
msgid "sent %s to %s"
msgstr "%s an %s verschickt"

#: tftp.c:712
#, c-format
msgid "error %d %s received from %s"
msgstr "Fehler %d %s von %s empfangen"

#: log.c:203
#, c-format
msgid "overflow: %d log entries lost"
msgstr "Überlauf: %d Protokolleinträge verloren"

#: log.c:281
#, c-format
msgid "log failed: %s"
msgstr "Protokollierung fehlgeschlagen: %s"

#: log.c:490
msgid "FAILED to start up"
msgstr "Start FEHLGESCHLAGEN"

#: conntrack.c:63
#, c-format
msgid "Conntrack connection mark retrieval failed: %s"
msgstr "\"Conntrack connection mark\"-Abruf fehlgeschlagen: %s"

#: dhcp6.c:51
#, c-format
msgid "cannot create DHCPv6 socket: %s"
msgstr "Kann DHCPv6-Socket nicht erzeugen: %s"

#: dhcp6.c:72
#, c-format
msgid "failed to set SO_REUSE{ADDR|PORT} on DHCPv6 socket: %s"
msgstr "kann SO_REUSE{ADDR|PORT} für DHCPv6-Socket nicht aktivieren: %s"

#: dhcp6.c:84
#, c-format
msgid "failed to bind DHCPv6 server socket: %s"
msgstr "Kann nicht an DHCPv6-Server-Socket binden: %s"

#: rfc3315.c:173
#, c-format
msgid "no address range available for DHCPv6 request from relay at %s"
msgstr "Kein Adressbereich verfügbar für die DHCPv6-Anfrage vom Relais bei %s"

#: rfc3315.c:182
#, c-format
msgid "no address range available for DHCPv6 request via %s"
msgstr "Kein Adressbereich verfügbar für die DHCPv6-Anfrage via %s"

#: rfc3315.c:317
#, c-format
msgid "%u available DHCPv6 subnet: %s/%d"
msgstr "%u verfügbares DHCPv6-Subnetz: %s/%d"

#: rfc3315.c:400
#, c-format
msgid "%u vendor class: %u"
msgstr "%u Herstellerklasse: %u"

#: rfc3315.c:448
#, c-format
msgid "%u client MAC address: %s"
msgstr "%u Klient MAC-Adresse: %s"

#: rfc3315.c:763 rfc3315.c:860
msgid "address unavailable"
msgstr "Adresse nicht verfügbar"

#: rfc3315.c:775 rfc3315.c:904 rfc3315.c:1273
msgid "success"
msgstr "Erfolg"

#: rfc3315.c:790 rfc3315.c:799 rfc3315.c:912 rfc3315.c:914 rfc3315.c:1048
msgid "no addresses available"
msgstr "keine Adressen verfügbar"

#: rfc3315.c:891
msgid "not on link"
msgstr "nicht on link"

#: rfc3315.c:980 rfc3315.c:1181 rfc3315.c:1262
msgid "no binding found"
msgstr "Keine Bindung gefunden"

#: rfc3315.c:1017
msgid "deprecated"
msgstr "veraltet"

#: rfc3315.c:1024
msgid "address invalid"
msgstr "Adresse ungültig"

#: rfc3315.c:1082 rfc3315.c:1084
msgid "confirm failed"
msgstr "Bestätigung fehlgeschlagen"

#: rfc3315.c:1099
msgid "all addresses still on link"
msgstr "Alle Adressen immer noch in Verbindung"

#: rfc3315.c:1190
msgid "release received"
msgstr "Freigabe empfangen"

#: rfc3315.c:2200
#, c-format
msgid "Cannot multicast DHCP relay via interface %s"
msgstr "Multicast-DHCP-Relay über Schnittstelle %s nicht möglich"

#: rfc3315.c:2216
#, c-format
msgid "multicast via %s"
msgstr "multicast via %s"

#: dhcp-common.c:187
#, c-format
msgid "Ignoring duplicate dhcp-option %d"
msgstr "Ignoriere doppelt vorhandene DHCP-Option %d"

#: dhcp-common.c:264
#, c-format
msgid "%u tags: %s"
msgstr "%u Marken: %s"

#: dhcp-common.c:484
#, c-format
msgid "%s has more than one address in hostsfile, using %s for DHCP"
msgstr "%s hat mehr als eine Adresse in hosts-Datei, benutze %s für DHCP"

#: dhcp-common.c:518
#, c-format
msgid "duplicate IP address %s (%s) in dhcp-config directive"
msgstr "doppelte IP-Adresse %s (%s) in \"dhcp-config\"-Anweisung"

#: dhcp-common.c:738
#, c-format
msgid "Known DHCP options:\n"
msgstr "Bekannte DHCP-Optionen:\n"

#: dhcp-common.c:749
#, c-format
msgid "Known DHCPv6 options:\n"
msgstr "Bekannte DHCPv6-Optionen:\n"

#: dhcp-common.c:946
msgid ", prefix deprecated"
msgstr ", Präfix veraltet"

#: dhcp-common.c:949
#, c-format
msgid ", lease time "
msgstr ", Leasezeit "

#: dhcp-common.c:991
#, c-format
msgid "%s stateless on %s%.0s%.0s%s"
msgstr "%s zustandslos auf %s%.0s%.0s%s"

#: dhcp-common.c:993
#, c-format
msgid "%s, static leases only on %.0s%s%s%.0s"
msgstr "%s, nur statische Leases auf %.0s%s%s%.0s"

#: dhcp-common.c:995
#, c-format
msgid "%s, proxy on subnet %.0s%s%.0s%.0s"
msgstr "%s, Proxy im Subnetz %.0s%s%.0s%.0s"

#: dhcp-common.c:996
#, c-format
msgid "%s, IP range %s -- %s%s%.0s"
msgstr "%s, IP-Bereich %s -- %s%s%.0s"

#: dhcp-common.c:1009
#, c-format
msgid "DHCPv4-derived IPv6 names on %s%s"
msgstr "DHCPv4-abgeleitete IPv6 Namen auf %s%s"

#: dhcp-common.c:1012
#, c-format
msgid "router advertisement on %s%s"
msgstr "Router-Advertisment auf %s%s"

#: dhcp-common.c:1043
#, c-format
msgid "DHCP relay from %s via %s"
msgstr "DHCP-Relay von %s über %s"

#: dhcp-common.c:1045
#, c-format
msgid "DHCP relay from %s to %s via %s"
msgstr "DHCP Weiterleitung von %s nach %s über %s"

#: dhcp-common.c:1048
#, c-format
msgid "DHCP relay from %s to %s"
msgstr "DHCP Weiterleitung von %s nach %s"

#: radv.c:110
#, c-format
msgid "cannot create ICMPv6 socket: %s"
msgstr "Kann ICMPv6-Socket nicht erzeugen: %s"

#: auth.c:462
#, c-format
msgid "ignoring zone transfer request from %s"
msgstr "ignoriere Zonentransfer-Anfrage von %s"

#: ipset.c:99
#, c-format
msgid "failed to create IPset control socket: %s"
msgstr "konnte IPset-Kontroll-Socket nicht erzeugen: %s"

#: ipset.c:211
#, c-format
msgid "failed to update ipset %s: %s"
msgstr "Aktualisierung von ipset %s fehlgeschlagen: %s"

#: pattern.c:29
#, c-format
msgid "[pattern.c:%d] Assertion failure: %s"
msgstr "[pattern.c:%d] Assertion failure: %s"

#: pattern.c:142
#, c-format
msgid "Invalid DNS name: Invalid character %c."
msgstr "Ungültiger DNS-Name: Ungültiges Zeichen %c."

#: pattern.c:151
msgid "Invalid DNS name: Empty label."
msgstr "Ungültiger DNS-Name: Leeres Label."

#: pattern.c:156
msgid "Invalid DNS name: Label starts with hyphen."
msgstr "Ungültiger DNS-Name: Label beginnt mit Bindestrich."

#: pattern.c:170
msgid "Invalid DNS name: Label ends with hyphen."
msgstr "Ungültiger DNS-Name: Bezeichnung endet mit Bindestrich."

#: pattern.c:176
#, c-format
msgid "Invalid DNS name: Label is too long (%zu)."
msgstr "Ungültiger DNS-Name: Label ist zu lang (%zu)."

#: pattern.c:184
#, c-format
msgid "Invalid DNS name: Not enough labels (%zu)."
msgstr "Ungültiger DNS-Name: Nicht genügend Labels (%zu)."

#: pattern.c:189
msgid "Invalid DNS name: Final label is fully numeric."
msgstr "Ungültiger DNS-Name: Das endgültige Label ist vollständig numerisch."

#: pattern.c:199
msgid "Invalid DNS name: \"local\" pseudo-TLD."
msgstr "Ungültiger DNS-Name: \"lokale\" Pseudo-TLD."

#: pattern.c:204
#, c-format
msgid "DNS name has invalid length (%zu)."
msgstr "Der DNS-Name hat eine ungültige Länge (%zu)."

#: pattern.c:258
#, c-format
msgid "Invalid DNS name pattern: Invalid character %c."
msgstr "Ungültiges DNS-Namensmuster: Ungültiges Zeichen %c."

#: pattern.c:267
msgid "Invalid DNS name pattern: Empty label."
msgstr "Ungültiges DNS-Namensmuster: Leeres Label."

#: pattern.c:272
msgid "Invalid DNS name pattern: Label starts with hyphen."
msgstr "Ungültiges DNS-Namensmuster: Bezeichnung beginnt mit Bindestrich."

#: pattern.c:285
msgid "Invalid DNS name pattern: Wildcard character used more than twice per label."
msgstr "Ungültiges DNS-Namensmuster: Platzhalterzeichen werden mehr als zweimal pro Label verwendet."

#: pattern.c:295
msgid "Invalid DNS name pattern: Label ends with hyphen."
msgstr "Ungültiges DNS-Namensmuster: Bezeichnung endet mit Bindestrich."

#: pattern.c:301
#, c-format
msgid "Invalid DNS name pattern: Label is too long (%zu)."
msgstr "Ungültiges DNS-Namensmuster: Label ist zu lang (%zu)."

#: pattern.c:309
#, c-format
msgid "Invalid DNS name pattern: Not enough labels (%zu)."
msgstr "Ungültiges DNS-Namensmuster: Nicht genügend Labels (%zu)."

#: pattern.c:314
msgid "Invalid DNS name pattern: Wildcard within final two labels."
msgstr "Ungültiges DNS-Namensmuster: Platzhalter innerhalb der letzten beiden Labels."

#: pattern.c:319
msgid "Invalid DNS name pattern: Final label is fully numeric."
msgstr "Ungültiges DNS-Namensmuster: Das endgültige Label ist vollständig numerisch."

#: pattern.c:329
msgid "Invalid DNS name pattern: \"local\" pseudo-TLD."
msgstr "Ungültiges DNS-Namensmuster: \"lokale\" Pseudo-TLD."

#: pattern.c:334
#, c-format
msgid "DNS name pattern has invalid length after removing wildcards (%zu)."
msgstr "Das DNS-Namensmuster hat nach dem Entfernen von Platzhaltern (%zu) eine ungültige Länge."

#: dnssec.c:206
msgid "system time considered valid, now checking DNSSEC signature timestamps."
msgstr "Systemzeit als gültig betrachtet, prüfe jetzt DNSSEC Signatur-Zeitstempel."

#: dnssec.c:1018
#, c-format
msgid "Insecure DS reply received for %s, check domain configuration and upstream DNS server DNSSEC support"
msgstr "Unsichere DS-Antwort für %s, bitte Domainkonfiguration und Upstream DNS-Server für DNSSEC-Unterstützung überprüfen"

#: blockdata.c:55
#, c-format
msgid "pool memory in use %zu, max %zu, allocated %zu"
msgstr "Speicherpool in Benutzung %zu, Max %zu, zugewiesen %zu"

#: tables.c:61
#, c-format
msgid "failed to access pf devices: %s"
msgstr "konnte auf pf-Geräte nicht zugreifen: %s"

#: tables.c:74
#, c-format
msgid "warning: no opened pf devices %s"
msgstr "Warnung: Keine geöffneten pf-Geräte %s"

#: tables.c:82
#, c-format
msgid "error: cannot use table name %s"
msgstr "Fehler: Kann Tabellenname %s nicht benutzen"

#: tables.c:90
#, c-format
msgid "error: cannot strlcpy table name %s"
msgstr "Fehler: Kann den Tabellennamen %s nicht mit strlcpy kopieren"

#: tables.c:101
#, c-format
msgid "IPset: error: %s"
msgstr "IPset: Fehler: %s"

#: tables.c:108
msgid "info: table created"
msgstr "Info: Tabelle erstellt"

#: tables.c:133
#, c-format
msgid "warning: DIOCR%sADDRS: %s"
msgstr "Warnung: DIOCR%sADDRS: %s"

#: tables.c:137
#, c-format
msgid "%d addresses %s"
msgstr "%d Adressen %s"

#: inotify.c:62
#, c-format
msgid "cannot access path %s: %s"
msgstr "Kann auf Pfad %s nicht zugreifen: %s"

#: inotify.c:95
#, c-format
msgid "failed to create inotify: %s"
msgstr "Kann kein \"inotify\" erzeugen: %s"

#: inotify.c:111
#, c-format
msgid "too many symlinks following %s"
msgstr "zu viele Symlinks beim Verfolgen von %s"

#: inotify.c:127
#, c-format
msgid "directory %s for resolv-file is missing, cannot poll"
msgstr "Verzeichnis %s für resolv-file fehlt, kann nicht abfragen"

#: inotify.c:131 inotify.c:200
#, c-format
msgid "failed to create inotify for %s: %s"
msgstr "Konnte \"inotify\" für %s nicht erzeugen: %s"

#: inotify.c:178 inotify.c:185
#, c-format
msgid "bad dynamic directory %s: %s"
msgstr "fehlerhaftes dynamisches Verzeichnis %s: %s"

#: inotify.c:186
msgid "not a directory"
msgstr "ist kein Verzeichnis"

#: inotify.c:299
#, c-format
msgid "inotify: %s removed"
msgstr "inotify: %s entfernt"

#: inotify.c:301
#, c-format
msgid "inotify: %s new or modified"
msgstr "inotify: %s neu oder modifiziert"

#: inotify.c:309
#, c-format
msgid "inotify: flushed %u names read from %s"
msgstr "inotify: %u Namen gelöscht, aus %s gelesen"

#: dump.c:68
#, c-format
msgid "cannot create %s: %s"
msgstr "kann %s nicht erstellen: %s"

#: dump.c:74
#, c-format
msgid "bad header in %s"
msgstr "Fehlerhafter Kopf in %s"

#: dump.c:287
msgid "failed to write packet dump"
msgstr "schreiben des Paketmitschnitts fehlgeschlagen"

#: dump.c:289
#, c-format
msgid "%u dumping packet %u mask 0x%04x"
msgstr "%u dumpe Paket %u Maske 0x%04x"

#: dump.c:291
#, c-format
msgid "dumping packet %u mask 0x%04x"
msgstr "Dumpe Packet %u Make 0x%04x"

#: ubus.c:79
#, c-format
msgid "UBus subscription callback: %s subscriber(s)"
msgstr "UBus-Subskription Rückruf: %s Teilnehmer"

#: ubus.c:99
#, c-format
msgid "Cannot reconnect to UBus: %s"
msgstr "Kann mit UBus nicht erneut verbinden: %s"

#: ubus.c:135
msgid "Cannot set UBus listeners: no connection"
msgstr "Kann UBus-Zuhörer nicht setzen: Keine Verbindung"

#: ubus.c:155
msgid "Cannot poll UBus listeners: no connection"
msgstr "Kann UBus-Zuhörer nicht abfragen: Keine Verbindung"

#: ubus.c:168
msgid "Disconnecting from UBus"
msgstr "Von System-UBus trennen"

#: ubus.c:179 ubus.c:326
#, c-format
msgid "UBus command failed: %d (%s)"
msgstr "UBus-Befehl fehlgeschlagen: %d (%s)"

#: hash-questions.c:40
msgid "Failed to create SHA-256 hash object"
msgstr "Kann SHA-256-Hash-Objekt nicht erstellen"

#: nftset.c:35
msgid "failed to create nftset context"
msgstr "Fehler beim Erstellen des NFTSET-Kontexts"

#~ msgid "bad IPv4 prefix"
#~ msgstr "fehlerhaftes IPv4-Präfix"

#~ msgid "Cannot initialize UBus: connection failed"
#~ msgstr "Kann UBus nicht initialisieren: Verbindung fehlgeschlagen"

#~ msgid "Cannot add object to UBus: %s"
#~ msgstr "Kann Objekt zu UBus nicht hinzufügen: %s"

#~ msgid "Failed to send UBus event: %s"
#~ msgstr "Fehlschlag beim Sendes des UBus-Ereignisses: %s"
